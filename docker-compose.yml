
services:
  # База данных PostgreSQL
  postgres:
    image: postgres:15
    container_name: messenger_postgres
    environment:
      POSTGRES_DB: messenger
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - messenger_network

  # Redis для кэширования и сессий
  redis:
    image: redis:7-alpine
    container_name: messenger_redis
    ports:
      - "6379:6379"
    networks:
      - messenger_network

  # MinIO для S3-совместимого хранилища файлов
  minio:
    image: minio/minio:latest
    container_name: messenger_minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - messenger_network

  # eJabberd XMPP сервер
  ejabberd:
    image: ghcr.io/processone/ejabberd:latest
    container_name: messenger_ejabberd
    environment:
      EJABBERD_DOMAIN: localhost
      EJABBERD_ADMIN_USER: admin
      EJABBERD_ADMIN_PASSWORD: admin123
    ports:
      - "5222:5222"  # XMPP C2S
      - "5269:5269"  # XMPP S2S
      - "5280:5280"  # HTTP API
      - "5443:5443"  # HTTPS
    volumes:
      - ./ejabberd/ejabberd.yml:/home/<USER>/conf/ejabberd.yml
      - ./ejabberd/database.yml:/home/<USER>/conf/database.yml
      - ejabberd_data:/home/<USER>/database
      - ejabberd_logs:/home/<USER>/logs
    depends_on:
      - postgres
    networks:
      - messenger_network

  # LiveKit сервер для видео/аудио звонков
  livekit:
    image: livekit/livekit-server:latest
    container_name: messenger_livekit
    command: --config /etc/livekit.yaml
    ports:
      - "7880:7880"  # RTC
      - "7881:7881"  # TURN
    volumes:
      - ./livekit/livekit.yaml:/etc/livekit.yaml
    environment:
      LIVEKIT_KEYS: "APIKey: secret123"
    networks:
      - messenger_network

  # FastAPI бекенд
  fastapi_backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: messenger_backend
    environment:
      DATABASE_URL: ***********************************************/messenger
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      EJABBERD_API_URL: http://ejabberd:5280/api
      EJABBERD_ADMIN_USER: admin
      EJABBERD_ADMIN_PASSWORD: admin123
      LIVEKIT_API_KEY: APIKey
      LIVEKIT_API_SECRET: secret123
      LIVEKIT_URL: ws://livekit:7880
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - minio
      - ejabberd
      - livekit
    volumes:
      - ./backend:/app
    networks:
      - messenger_network

volumes:
  postgres_data:
  minio_data:
  ejabberd_data:
  ejabberd_logs:

networks:
  messenger_network:
    driver: bridge